from pysnmp.entity import engine, config
from pysnmp.entity.rfc3413 import cmdrsp, context
from pysnmp.proto import rfc1902
from pysnmp.carrier.asyncore.dgram import udp
from pysnmp.proto.api import v2c
from pymodbus.client import ModbusTcpClient
import threading
import time
import logging


# 配置 Modbus 连接参数
MODBUS_SERVER_IP = "*************"  # Modbus 设备 IP
MODBUS_PORT = 502                   # Modbus 端口
MODBUS_REGISTER_ADDRESS = 0x100      # 目标寄存器地址 (0x100)
MODBUS_UNIT_ID = 1                  # 从站地址

# 配置 SNMPv2 参数
SNMP_COMMUNITY = "public"           # 社区字符串
SNMP_OID_MAPPING = "*******.4.1.99999.1.0"  # 自定义 OID

# 创建 SNMP 引擎
snmp_engine = engine.SnmpEngine()

# 配置 SNMPv2 社区访问权限
config.addV1System(snmp_engine, "my-area", SNMP_COMMUNITY)

# 添加 SNMP 上下文
snmp_context = context.SnmpContext(snmp_engine)

# 定义 Modbus 数据读取函数
def read_modbus_register():
    """读取 Modbus 寄存器数据"""
    client = ModbusTcpClient(MODBUS_SERVER_IP, port=MODBUS_PORT)
    try:
        if client.connect():
            # 功能码 03 (读保持寄存器)，地址 0x100 (十进制 256)
            response = client.read_holding_registers(MODBUS_REGISTER_ADDRESS, 1, unit=MODBUS_UNIT_ID)
            if not response.isError():
                value = response.registers[0]
                print(f"成功读取 Modbus 寄存器 0x{MODBUS_REGISTER_ADDRESS:X}: {value}")
                return value
            else:
                raise Exception(f"Modbus 读取错误: {response}")
        else:
            raise Exception(f"无法连接到 Modbus 服务器 {MODBUS_SERVER_IP}:{MODBUS_PORT}")
    except Exception as e:
        print(f"Modbus 连接或读取失败: {e}")
        raise
    finally:
        client.close()

# 自定义 SNMP 命令响应器
class ModbusDataResponder(cmdrsp.GetCommandResponder):
    """处理 SNMP GET 请求并返回 Modbus 数据"""
    def handleMgmtOperation(self, snmp_engine, state_reference, context_name,
                            var_binds, cb_ctx):
        # 遍历请求的 OID
        response_var_binds = []
        for oid, val in var_binds:
            if str(oid) == SNMP_OID_MAPPING:
                try:
                    # 读取 Modbus 数据
                    modbus_value = read_modbus_register()
                    # 封装为 SNMP 整数类型
                    response_var = rfc1902.Integer(modbus_value)
                    response_var_binds.append((oid, response_var))
                    print(f"返回 Modbus 数据: {modbus_value}")
                except Exception as e:
                    print(f"读取 Modbus 数据失败: {e}")
                    # 返回错误值
                    response_var_binds.append((oid, rfc1902.Integer(-1)))
            else:
                # OID 不匹配，返回 noSuchObject
                response_var_binds.append((oid, rfc1902.noSuchObject))

        # 发送响应
        self.sendRsp(snmp_engine, state_reference, 0, 0, response_var_binds)

# 注册响应器到 SNMP 引擎
ModbusDataResponder(snmp_engine, snmp_context)

# 启动 SNMP Agent
def start_snmp_agent():
    # 配置UDP传输
    config.addTransport(
        snmp_engine,
        udp.domainName,
        udp.UdpTransport().openServerMode(('0.0.0.0', 161))
    )

    print("SNMP Agent 已启动，监听端口 161")
    try:
        snmp_engine.transportDispatcher.jobStarted(1)
        snmp_engine.transportDispatcher.runDispatcher()
    except Exception as e:
        print(f"SNMP Agent 启动失败: {e}")
        snmp_engine.transportDispatcher.closeDispatcher()
        raise

# 主程序
if __name__ == "__main__":
    # 后台启动 SNMP Agent
    snmp_thread = threading.Thread(target=start_snmp_agent)
    snmp_thread.daemon = True
    snmp_thread.start()
    print("按 Ctrl+C 停止服务")
    try:
        while True:
            pass  # 保持主线程运行
    except KeyboardInterrupt:
        print("服务已停止")
